2025-05-12 14:50:52,360 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:10:54,455 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:31:22,187 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:42:34,376 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:44:38,282 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 15:45:53,415 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 16:00:12,856 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register
2025-05-12 16:01:41,834 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register, error: Signature verification failed
2025-05-12 16:02:53,049 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/register, error: Signature verification failed
2025-05-12 16:50:05,923 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:05,924 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:32,482 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:32,796 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:50:58,735 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/register
2025-05-12 16:58:44,024 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:58:44,026 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-12 16:59:39,755 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users
2025-05-12 17:29:46,705 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:29:46,967 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:29:55,622 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:30:11,135 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:30:11,232 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /favicon.ico
2025-05-12 17:30:53,337 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:30:53,643 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:31:18,445 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:36:17,059 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 17:36:17,323 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:04:37,506 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:04:37,770 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:28,666 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:28,928 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:42,778 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:10:43,082 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:09,773 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:10,036 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:18,851 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:19,166 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:27,342 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:16:27,603 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:17:17,886 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:19:12,247 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:21:04,149 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:21:33,306 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:28,514 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:28,779 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:34,440 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:34,744 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:39,873 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:42,620 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:44,917 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:45,181 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:46,099 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:46,407 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:47,482 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:47,747 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:58,678 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:22:58,984 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:24:21,823 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:24:46,414 - user_service.common.utils - ERROR - Error: You don't have permission to view all users, Status: 403
2025-05-12 18:31:26,477 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-12 18:31:35,821 - user_service.common.utils - ERROR - Error: Email already exists, Status: 400
2025-05-13 14:40:06,098 - user_service.common.utils - ERROR - Error: You don't have permission to register a Student, Status: 403
2025-05-13 14:40:29,716 - user_service.common.utils - ERROR - Error: You don't have permission to register a Teacher, Status: 403
2025-05-13 17:06:08,782 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-27 13:27:13,512 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-27 15:40:41,146 - user_service.common.utils - ERROR - Error: Email already exists, Status: 400
2025-05-27 18:48:33,570 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-28 11:00:41,712 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-28 11:06:36,193 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-28 11:12:50,005 - user_service.common.utils - ERROR - Error: Email already exists, Status: 400
2025-05-29 11:29:55,792 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-29 11:30:03,888 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/users, error: Not enough segments
2025-05-29 11:49:12,370 - user_service.common.utils - ERROR - Error: Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or hyphen only, Status: 400
2025-05-29 11:49:14,402 - user_service.common.utils - ERROR - Error: Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or hyphen only, Status: 400
2025-05-29 11:49:16,431 - user_service.common.utils - ERROR - Error: Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or hyphen only, Status: 400
2025-05-29 11:49:18,453 - user_service.common.utils - ERROR - Error: Invalid input: Invalid email address format, Status: 400
2025-05-29 11:49:20,494 - user_service.common.utils - ERROR - Error: Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or hyphen only, Status: 400
2025-05-29 11:49:22,509 - user_service.common.utils - ERROR - Error: Invalid input: Invalid email address format, Status: 400
2025-05-29 11:49:24,541 - user_service.common.utils - ERROR - Error: Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or hyphen only, Status: 400
2025-05-29 11:49:26,557 - user_service.common.utils - ERROR - Error: Invalid input: Invalid email address format, Status: 400
2025-05-29 11:49:28,577 - user_service.common.utils - ERROR - Error: Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or hyphen only, Status: 400
2025-05-29 11:49:30,605 - user_service.common.utils - ERROR - Error: Invalid input: Invalid email address format, Status: 400
2025-05-29 11:49:32,631 - user_service.common.middleware - WARNING - Missing or invalid Authorization header for path: /api/users/users
2025-05-29 11:49:40,771 - user_service.common.middleware - WARNING - Invalid token for path: /api/users/users, error: Not enough segments
2025-05-29 12:20:53,435 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
2025-05-29 12:21:06,266 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
2025-05-30 10:45:44,964 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
2025-05-30 10:46:06,225 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
2025-05-30 10:54:45,153 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
2025-05-30 10:55:02,276 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-30 10:55:26,051 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-30 10:55:41,148 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-30 10:59:19,055 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
2025-05-30 10:59:43,106 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
2025-05-30 11:00:03,000 - user_service.common.utils - ERROR - Error: Username already exists, Status: 400
2025-05-30 11:06:39,150 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
2025-05-30 11:06:44,100 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
2025-05-30 11:06:45,335 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
2025-05-30 11:34:56,148 - user_service.common.utils - ERROR - Error: Invalid input: Password must contain at least one digit, Status: 400
